#!/usr/bin/env python3
"""
Main orchestration script for video gait analysis platform.
"""
import sys
import argparse
from pathlib import Path
from typing import Dict, Any, List
import json
import time

# Setup logging
from loguru import logger

# Import configuration and modules
from config import get_config, INPUT_VIDEOS_DIR, OUTPUT_RESULTS_DIR
from modules import utils, VideoProcessor, GaitCycleDetector, BiomechanicalAnalyzer

def setup_logging(config: Dict[str, Any]) -> None:
    """Setup logging configuration."""
    log_config = config.get("logging", {})

    # Create logs directory
    log_file = Path(log_config.get("log_file", "logs/gait_analysis.log"))
    log_file.parent.mkdir(exist_ok=True)

    # Configure loguru
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        level=log_config.get("level", "INFO"),
        format=log_config.get("format", "{time} | {level} | {message}")
    )
    logger.add(
        log_file,
        level=log_config.get("level", "INFO"),
        format=log_config.get("format", "{time} | {level} | {message}"),
        rotation=log_config.get("rotation", "10 MB"),
        retention=log_config.get("retention", "1 week")
    )

def process_single_video(video_path: Path, config: Dict[str, Any], output_base_dir: Path = None, visualize: bool = False) -> Dict[str, Any]:
    """
    Process a single video file and extract gait measurements.

    Args:
        video_path: Path to video file
        config: Configuration dictionary
        output_base_dir: Base directory for output (optional)
        visualize: Whether to generate visualization outputs

    Returns:
        Analysis results dictionary
    """
    logger.info(f"Starting analysis of video: {video_path}")
    start_time = time.time()

    try:
        # Create output directory for this video
        video_name = video_path.stem
        base_dir = output_base_dir if output_base_dir else OUTPUT_RESULTS_DIR
        output_dir = utils.general.mkdir(base_dir=base_dir, video_name=video_name)

        # Initialize processors
        video_processor = VideoProcessor(config)
        gait_detector = GaitCycleDetector(config)
        biomech_analyzer = BiomechanicalAnalyzer(config)

        # Step 1: Process video with selected pose detection engine
        pose_engine = config.get("pose_detection_engine", "openpose")
        logger.info(f"Step 1: Processing video with {pose_engine.upper()}...")
        pose_results = video_processor.process_video(video_path, output_dir, visualize=visualize)

        # Extract primary person keypoints
        keypoints_sequence = video_processor.get_primary_person_keypoints(
            pose_results["keypoints_data"]
        )

        if not keypoints_sequence:
            raise ValueError("No valid keypoints detected in video")

        fps = pose_results["video_properties"]["fps"]

        # Step 2: Detect gait cycles
        logger.info("Step 2: Detecting gait cycles...")
        gait_data = gait_detector.detect_gait_cycles(keypoints_sequence, fps)

        if not gait_data["cycles"]:
            logger.warning("No complete gait cycles detected")

        # Step 3: Calculate biomechanical measurements
        logger.info("Step 3: Calculating biomechanical measurements...")
        measurements = biomech_analyzer.analyze_gait_measurements(
            keypoints_sequence, gait_data, fps
        )

        # Step 4: Generate visualizations if requested
        visualization_files = []
        if visualize:
            logger.info("Step 4: Generating visualizations...")
            from modules.visualizer import GaitVisualizer
            visualizer = GaitVisualizer(config)
            visualization_files = visualizer.create_visualizations(
                video_path, output_dir, pose_results, gait_data, keypoints_sequence, fps
            )

        # Compile final results
        results = {
            "video_info": {
                "path": str(video_path),
                "name": video_name,
                "properties": pose_results["video_properties"],
            },
            "processing_info": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "processing_time_seconds": time.time() - start_time,
                "total_frames": len(keypoints_sequence),
                "frames_with_detection": pose_results["frames_with_detection"],
                "detection_rate": pose_results["frames_with_detection"] / len(keypoints_sequence) if keypoints_sequence else 0,
            },
            "gait_analysis": {
                "cycles": gait_data["cycles"],
                "phases": gait_data["phases"],
                "events": gait_data["events"],
                "total_cycles": gait_data["total_cycles"],
            },
            "biomechanical_measurements": measurements,
            "output_directory": str(output_dir),
            "visualization_files": visualization_files if visualize else [],
        }

        # Save comprehensive results
        results_file = output_dir / "gait_analysis_results.json"
        try:
            utils.json.dump(results, results_file)
            logger.info(f"Results saved to {results_file}")
        except Exception as e:
            logger.error(f"Failed to save results: {e}")

        # Save summary measurements only
        summary_file = output_dir / "measurements_summary.json"
        try:
            utils.json.dump(measurements, summary_file)
            logger.info(f"Results saved to {summary_file}")
        except Exception as e:
            logger.error(f"Failed to save results: {e}")

        # Cleanup
        video_processor.cleanup()

        processing_time = time.time() - start_time
        logger.info(f"Video analysis completed in {processing_time:.2f} seconds")
        logger.info(f"Results saved to: {output_dir}")

        return results

    except Exception as e:
        logger.error(f"Error processing video {video_path}: {e}")
        return {
            "video_info": {"path": str(video_path), "name": video_path.stem},
            "error": str(e),
            "processing_time_seconds": time.time() - start_time,
        }

def process_batch_videos(input_dir: Path, config: Dict[str, Any], output_base_dir: Path = None, visualize: bool = False) -> List[Dict[str, Any]]:
    """
    Process all videos in the input directory.

    Args:
        input_dir: Directory containing video files
        config: Configuration dictionary

    Returns:
        List of analysis results for each video
    """
    logger.info(f"Processing batch videos from: {input_dir}")

    # Find all video files
    supported_formats = config["video"]["supported_formats"]
    video_files = []

    for format_ext in supported_formats:
        video_files.extend(input_dir.glob(f"*{format_ext}"))
        video_files.extend(input_dir.glob(f"*{format_ext.upper()}"))

    if not video_files:
        logger.warning(f"No video files found in {input_dir}")
        return []

    logger.info(f"Found {len(video_files)} video files to process")

    # Process each video
    all_results = []
    for i, video_path in enumerate(video_files, 1):
        logger.info(f"Processing video {i}/{len(video_files)}: {video_path.name}")

        try:
            results = process_single_video(video_path, config, output_base_dir, visualize)
            all_results.append(results)
        except Exception as e:
            logger.error(f"Failed to process {video_path}: {e}")
            all_results.append({
                "video_info": {"path": str(video_path), "name": video_path.stem},
                "error": str(e)
            })

    # Save batch summary
    base_dir = output_base_dir if output_base_dir else OUTPUT_RESULTS_DIR
    batch_summary_file = base_dir / "batch_analysis_summary.json"
    batch_data = {
        "batch_info": {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_videos": len(video_files),
            "successful_analyses": len([r for r in all_results if "error" not in r]),
            "failed_analyses": len([r for r in all_results if "error" in r]),
        },
        "results": all_results
    }
    try:
        utils.json.dump(batch_data, batch_summary_file)
        logger.info(f"Batch summary saved to {batch_summary_file}")
    except Exception as e:
        logger.error(f"Failed to save batch summary: {e}")

    logger.info(f"Batch processing completed. Summary saved to: {batch_summary_file}")
    return all_results

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Video Gait Analysis Platform using OpenPose",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
        Examples:
        # Process single video
        python main.py --video path/to/video.mp4

        # Process all videos in input directory
        python main.py --batch

        # Process with custom input directory
        python main.py --batch --input-dir /path/to/videos

        # Process with custom output directory
        python main.py --video video.mp4 --output-dir /path/to/results
        """
    )

    parser.add_argument(
        "--video", "-v",
        type=Path,
        help="Path to single video file to process"
    )

    parser.add_argument(
        "--batch", "-b",
        action="store_true",
        help="Process all videos in input directory"
    )

    parser.add_argument(
        "--input-dir", "-i",
        type=Path,
        default=INPUT_VIDEOS_DIR,
        help=f"Input directory containing videos (default: {INPUT_VIDEOS_DIR})"
    )

    parser.add_argument(
        "--output-dir", "-o",
        type=Path,
        default=OUTPUT_RESULTS_DIR,
        help=f"Output directory for results (default: {OUTPUT_RESULTS_DIR})"
    )

    parser.add_argument(
        "--config", "-c",
        type=Path,
        help="Path to custom configuration file (JSON)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )

    parser.add_argument(
        "--visualize-results",
        action="store_true",
        help="Generate visualization video with pose overlay and gait event images"
    )

    parser.add_argument(
        "--pose-engine",
        choices=["openpose", "vitpose"],
        help="Pose detection engine to use (openpose or vitpose). Overrides config setting."
    )

    args = parser.parse_args()

    # Load configuration
    config = get_config()

    # Override with custom config if provided
    if args.config and args.config.exists():
        with open(args.config) as f:
            custom_config = json.load(f)
            config.update(custom_config)

    # Override log level
    config["logging"]["level"] = args.log_level

    # Override pose detection engine if specified
    if args.pose_engine:
        config["pose_detection_engine"] = args.pose_engine

    # Setup logging
    setup_logging(config)

    # Update output directory if specified
    output_results_dir = OUTPUT_RESULTS_DIR
    if args.output_dir != OUTPUT_RESULTS_DIR:
        output_results_dir = args.output_dir
        output_results_dir.mkdir(exist_ok=True)

    logger.info("=== Video Gait Analysis Platform ===")
    logger.info(f"Pose detection engine: {config.get('pose_detection_engine', 'openpose')}")
    logger.info(f"OpenPose path: {config['paths']['openpose_root']}")
    if config.get('pose_detection_engine') == 'vitpose':
        logger.info(f"ViTPose path: {config['paths']['vitpose_root']}")
    logger.info(f"Input directory: {args.input_dir}")
    logger.info(f"Output directory: {output_results_dir}")

    try:
        if args.video:
            results = process_single_video(args.video, config, output_results_dir, args.visualize_results)
            if "error" in results:
                logger.error("Video processing failed")
                sys.exit(1)
            else:
                logger.info("Video processing completed successfully")
        elif args.batch:
            if not args.input_dir.exists():
                logger.error(f"Input directory not found: {args.input_dir}")
                sys.exit(1)

            results = process_batch_videos(args.input_dir, config, output_results_dir, args.visualize_results)

            failed_count = len([r for r in results if "error" in r])
            if failed_count > 0:
                logger.warning(f"{failed_count} videos failed to process")

            logger.info("Batch processing completed")

        else:
            logger.error("Please specify either --video or --batch")
            parser.print_help()
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
