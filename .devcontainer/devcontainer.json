{
  "name": "Gait Analysis Dev",
  "dockerComposeFile": "../docker-compose.yml",
  "service": "gait-analysis",
  "workspaceFolder": "/app",
  "shutdownAction": "stopCompose",
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-python.python",
        "ms-python.pylint",
        "ms-python.black-formatter",
        "ms-python.mypy-type-checker",
        "ms-toolsai.jupyter",
        "ms-vscode.cmake-tools"
      ],
      "settings": {
        "python.defaultInterpreterPath": "/usr/bin/python3",
        "python.formatting.provider": "black",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": true
      }
    }
  },
  "forwardPorts": [
    8000
  ], // auto forward FastAPI port
  "postCreateCommand": "python3 test_setup.py",
  // git x devcontainer setup
  "mounts": [
    "source=${localEnv:HOME}/.ssh,target=/root/.ssh,type=bind,consistency=cached"
  ],
  "features": {
    "ghcr.io/devcontainers/features/common-utils:2": {
      "installZsh": true,
      "username": "vscode",
      "userUid": "1000",
      "userGid": "1000"
    }
  }
}