#!/bin/bash
# Setup script for ViTPose installation and model preparation
# This script installs ViTPose, MMCV, and downloads the required models

set -e  # Exit on any error

echo "🚀 Starting ViTPose setup..."

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Define paths
VITPOSE_ROOT="$PROJECT_ROOT/ViTPose"
MMCV_ROOT="$PROJECT_ROOT/mmcv"
MODELS_DIR="$VITPOSE_ROOT/models"

echo "📁 Project root: $PROJECT_ROOT"
echo "📁 ViTPose root: $VITPOSE_ROOT"
echo "📁 MMCV root: $MMCV_ROOT"

# Check if CUDA is available
if command -v nvidia-smi &> /dev/null; then
    echo "✅ CUDA detected"
    CUDA_VERSION=$(nvidia-smi | grep "CUDA Version" | awk '{print $9}' | cut -d'.' -f1,2)
    echo "   CUDA Version: $CUDA_VERSION"
else
    echo "⚠️  CUDA not detected. ViTPose will run on CPU (slower performance)"
    CUDA_VERSION="cpu"
fi

# Function to check if a Python package is installed
check_python_package() {
    python3 -c "import $1" 2>/dev/null && echo "✅ $1 is installed" || echo "❌ $1 is not installed"
}

# Check Python dependencies
echo "🔍 Checking Python dependencies..."
check_python_package "torch"
check_python_package "torchvision"

# Install PyTorch if not available
if ! python3 -c "import torch" 2>/dev/null; then
    echo "📦 Installing PyTorch..."
    if [ "$CUDA_VERSION" != "cpu" ]; then
        pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    else
        pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    fi
fi

# Install additional dependencies
echo "📦 Installing additional dependencies..."
pip3 install timm==0.4.9 einops

# Clone and install MMCV
if [ ! -d "$MMCV_ROOT" ]; then
    echo "📥 Cloning MMCV..."
    cd "$PROJECT_ROOT"
    git clone https://github.com/open-mmlab/mmcv.git
    cd mmcv
    git checkout v1.3.9
else
    echo "✅ MMCV directory already exists"
    cd "$MMCV_ROOT"
fi

# Install MMCV with custom operations
echo "🔨 Installing MMCV with custom operations..."
if [ "$CUDA_VERSION" != "cpu" ]; then
    MMCV_WITH_OPS=1 pip3 install -e .
else
    pip3 install -e .
fi

# Clone and install ViTPose
if [ ! -d "$VITPOSE_ROOT" ]; then
    echo "📥 Cloning ViTPose..."
    cd "$PROJECT_ROOT"
    git clone https://github.com/ViTAE-Transformer/ViTPose.git
else
    echo "✅ ViTPose directory already exists"
fi

cd "$VITPOSE_ROOT"

# Install ViTPose dependencies
echo "📦 Installing ViTPose dependencies..."
pip3 install -r requirements.txt

# Install MMPose
echo "📦 Installing MMPose..."
pip3 install mmpose

# Create models directory
mkdir -p "$MODELS_DIR"

# Download ViTPose+-H model
echo "📥 Downloading ViTPose+-H model..."
cd "$MODELS_DIR"

# Download the pre-trained model
MODEL_URL="https://1drv.ms/u/s!AimBgYV7JjTlgccyOIbEJAGQbKjKGw?e=b9etnD"
MODEL_FILE="vitpose-h-multi-coco.pth"

if [ ! -f "$MODEL_FILE" ]; then
    echo "⚠️  Please manually download the ViTPose+-H model from:"
    echo "   $MODEL_URL"
    echo "   Save it as: $MODELS_DIR/$MODEL_FILE"
    echo ""
    echo "   Alternatively, you can use wget if you have a direct download link:"
    echo "   wget -O $MODEL_FILE [DIRECT_DOWNLOAD_URL]"
else
    echo "✅ Model file already exists: $MODEL_FILE"
fi

# Run model split script if it exists
if [ -f "$VITPOSE_ROOT/tools/model_split.py" ]; then
    echo "🔧 Running model split script..."
    cd "$VITPOSE_ROOT"
    python3 tools/model_split.py --model-path "$MODELS_DIR/$MODEL_FILE"
else
    echo "⚠️  Model split script not found. You may need to run it manually."
fi

# Create a simple test script
echo "📝 Creating ViTPose test script..."
cat > "$VITPOSE_ROOT/test_vitpose.py" << 'EOF'
#!/usr/bin/env python3
"""
Simple test script for ViTPose installation.
"""
import sys
import os

def test_vitpose():
    """Test ViTPose installation."""
    try:
        # Test basic imports
        import torch
        print(f"✅ PyTorch {torch.__version__}")
        
        import mmcv
        print(f"✅ MMCV {mmcv.__version__}")
        
        import mmpose
        print(f"✅ MMPose {mmpose.__version__}")
        
        # Test CUDA availability
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA not available, using CPU")
        
        # Test ViTPose config
        config_file = "configs/body/2d_kpt_sview_rgb_img/topdown_heatmap/coco/ViTPose_huge_coco_256x192.py"
        if os.path.exists(config_file):
            print(f"✅ ViTPose config found: {config_file}")
        else:
            print(f"❌ ViTPose config not found: {config_file}")
        
        # Test model file
        model_file = "models/vitpose-h-multi-coco.pth"
        if os.path.exists(model_file):
            print(f"✅ ViTPose model found: {model_file}")
        else:
            print(f"❌ ViTPose model not found: {model_file}")
            print("   Please download the model manually.")
        
        print("\n🎉 ViTPose installation test completed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_vitpose()
    sys.exit(0 if success else 1)
EOF

chmod +x "$VITPOSE_ROOT/test_vitpose.py"

# Set up environment variables
echo "🔧 Setting up environment variables..."
echo ""
echo "Add the following to your ~/.bashrc or environment:"
echo "export VITPOSE_ROOT=\"$VITPOSE_ROOT\""
echo "export MMCV_ROOT=\"$MMCV_ROOT\""
echo "export PYTHONPATH=\"\$VITPOSE_ROOT:\$MMCV_ROOT:\$PYTHONPATH\""

# Final instructions
echo ""
echo "🎉 ViTPose setup completed!"
echo ""
echo "Next steps:"
echo "1. Download the ViTPose+-H model if not already done:"
echo "   URL: $MODEL_URL"
echo "   Save as: $MODELS_DIR/$MODEL_FILE"
echo ""
echo "2. Test the installation:"
echo "   cd $VITPOSE_ROOT"
echo "   python3 test_vitpose.py"
echo ""
echo "3. Update your configuration to use ViTPose:"
echo "   export POSE_DETECTION_ENGINE=vitpose"
echo ""
echo "4. Run your gait analysis with ViTPose!"
