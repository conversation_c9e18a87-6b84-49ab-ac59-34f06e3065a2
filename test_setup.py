#!/usr/bin/env python3
"""
Test script to verify the gait analysis platform setup.
"""
import sys
import traceback
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing imports...")

    try:
        # Test configuration
        from config import get_config
        config = get_config()
        print("✅ Configuration loaded successfully")

        # Test modules
        from modules import VideoProcessor, GaitCycleDetector, BiomechanicalAnalyzer
        print("✅ Analysis modules imported successfully")

        # Test utilities
        from modules.utils import euclidean_distance, calculate_angle
        print("✅ Utility functions imported successfully")

        # Test API models
        from api.models import AnalysisRequest, AnalysisResponse
        print("✅ API models imported successfully")

        return True

    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def test_openpose():
    """Test OpenPose availability."""
    print("\n🔍 Testing OpenPose...")

    try:
        import os
        from pathlib import Path

        # Get the current directory and construct paths
        current_dir = Path(__file__).parent
        openpose_root = current_dir.parent / "openpose"
        openpose_python_path = openpose_root / "build" / "python"
        openpose_lib_path = openpose_root / "build" / "src" / "openpose"
        caffe_lib_path = openpose_root / "build" / "caffe" / "lib"

        # Add OpenPose Python path to sys.path
        sys.path.append(str(openpose_python_path))

        # Add OpenPose library path to LD_LIBRARY_PATH
        current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
        if str(openpose_lib_path) not in current_ld_path:
            if current_ld_path:
                os.environ['LD_LIBRARY_PATH'] = f"{openpose_lib_path}:{current_ld_path}"
            else:
                os.environ['LD_LIBRARY_PATH'] = str(openpose_lib_path)

        # Add Caffe library path to LD_LIBRARY_PATH
        current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
        if str(caffe_lib_path) not in current_ld_path:
            if current_ld_path:
                os.environ['LD_LIBRARY_PATH'] = f"{caffe_lib_path}:{current_ld_path}"
            else:
                os.environ['LD_LIBRARY_PATH'] = str(caffe_lib_path)

        print(f"✅ Added OpenPose library path: {openpose_lib_path}")
        print(f"✅ Added Caffe library path: {caffe_lib_path}")
        print(f"✅ Added OpenPose Python path: {openpose_python_path}")

        from openpose import pyopenpose as op
        print("✅ OpenPose Python API imported successfully")

        # Test basic initialization
        params = dict()
        params["model_folder"] = str(openpose_root / "models") + "/"
        params["num_gpu"] = 1

        opWrapper = op.WrapperPython()
        opWrapper.configure(params)
        print("✅ OpenPose wrapper configured successfully")

        return True

    except Exception as e:
        print(f"❌ OpenPose error: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration values."""
    print("\n🔍 Testing configuration...")

    try:
        from config import get_config, OPENPOSE_ROOT, INPUT_VIDEOS_DIR, OUTPUT_RESULTS_DIR

        config = get_config()

        # Check required sections
        required_sections = ["openpose", "video", "gait", "keypoints", "api"]
        for section in required_sections:
            if section not in config:
                print(f"❌ Missing config section: {section}")
                return False

        print("✅ All required config sections present")

        # Check paths
        if not OPENPOSE_ROOT.exists():
            print(f"❌ OpenPose root not found: {OPENPOSE_ROOT}")
            return False

        print(f"✅ OpenPose root found: {OPENPOSE_ROOT}")

        # Check directories are created
        if not INPUT_VIDEOS_DIR.exists():
            print(f"❌ Input videos directory not found: {INPUT_VIDEOS_DIR}")
            return False

        if not OUTPUT_RESULTS_DIR.exists():
            print(f"❌ Output results directory not found: {OUTPUT_RESULTS_DIR}")
            return False

        print(f"✅ Input directory: {INPUT_VIDEOS_DIR}")
        print(f"✅ Output directory: {OUTPUT_RESULTS_DIR}")

        return True

    except Exception as e:
        print(f"❌ Configuration error: {e}")
        traceback.print_exc()
        return False

def test_dependencies():
    """Test required Python dependencies."""
    print("\n🔍 Testing dependencies...")

    required_packages = [
        ("numpy", "numpy"),
        ("opencv-python", "cv2"),
        ("scipy", "scipy"),
        ("pandas", "pandas"),
        ("matplotlib", "matplotlib"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("loguru", "loguru"),
        ("tqdm", "tqdm")
    ]

    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} - MISSING")
            missing_packages.append(package_name)

    if missing_packages:
        print(f"\n❌ Missing packages: {missing_packages}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False

    print("✅ All required dependencies available")
    return True

def test_gpu():
    """Test GPU availability."""
    print("\n🔍 Testing GPU...")

    try:
        import subprocess
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ NVIDIA GPU detected")
            # Extract GPU info
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Tesla' in line or 'GeForce' in line or 'Quadro' in line:
                    print(f"   GPU: {line.strip()}")
                    break
            return True
        else:
            print("❌ nvidia-smi failed - GPU may not be available")
            return False

    except FileNotFoundError:
        print("❌ nvidia-smi not found - NVIDIA drivers may not be installed")
        return False
    except Exception as e:
        print(f"❌ GPU test error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality with mock data."""
    print("\n🔍 Testing basic functionality...")

    try:
        from config import get_config
        from modules.utils import euclidean_distance, calculate_angle
        from modules.biomechanical_analyzer import BiomechanicalAnalyzer

        # Test utility functions
        distance = euclidean_distance((0, 0), (3, 4))
        if abs(distance - 5.0) > 1e-6:
            print(f"❌ Distance calculation error: expected 5.0, got {distance}")
            return False

        angle = calculate_angle((0, 1), (0, 0), (1, 0))
        if abs(angle - 90.0) > 1e-6:
            print(f"❌ Angle calculation error: expected 90.0, got {angle}")
            return False

        print("✅ Utility functions working correctly")

        # Test analyzer initialization
        config = get_config()
        analyzer = BiomechanicalAnalyzer(config)

        # Test posterior stability score calculation
        measurements = {
            "footDrift": 10.0,
            "kneeDrift": 1.2,
            "heelWhip": 5.0,
            "pelvicDrop": 2.0
        }

        score = analyzer.calculate_posterior_stability_score(measurements)
        if not isinstance(score, float) or score < 0:
            print(f"❌ Posterior stability score error: {score}")
            return False

        print("✅ Basic functionality tests passed")
        return True

    except Exception as e:
        print(f"❌ Basic functionality error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Gait Analysis Platform Setup Test")
    print("=" * 50)

    tests = [
        ("Dependencies", test_dependencies),
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("GPU", test_gpu),
        ("OpenPose", test_openpose),
        ("Basic Functionality", test_basic_functionality),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All tests passed! The platform is ready to use.")
        print("\nNext steps:")
        print("1. Place a test video in inputVideos/")
        print("2. Run: python main.py --video inputVideos/test_video.mp4")
        print("3. Check results in outputResults/")
        print("4. Start API: python -m uvicorn api.main:app --reload")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please fix the issues before proceeding.")
        print("\nRefer to DEVELOPMENT_ROADMAP.md for troubleshooting steps.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
