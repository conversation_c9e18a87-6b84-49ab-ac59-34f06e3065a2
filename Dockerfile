# Multi-stage Dockerfile for Gait Analysis Platform w/ OpenPose
# https://hub.docker.com/layers/nvidia/cuda/11.5.2-cudnn8-devel-ubuntu20.04/images/sha256-d29799715646a30c652487078ec85d9f681a361204d2fd934a416df37512ae79
FROM nvidia/cuda:11.5.2-cudnn8-devel-ubuntu20.04 AS base

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    cmake \
    build-essential \
    libopencv-dev \
    libprotobuf-dev \
    protobuf-compiler \
    libgoogle-glog-dev \
    libgflags-dev \
    libhdf5-dev \
    libatlas-base-dev \
    libboost-all-dev \
    # libcaffe-cuda-dev \
    wget \
    git \
    pkg-config \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Build OpenPose
WORKDIR /openpose
RUN git clone https://github.com/CMU-Perceptual-Computing-Lab/openpose.git .

WORKDIR /openpose/build
RUN cmake -DBUILD_PYTHON=ON .. && make -j `nproc`

# Set working directory for your application
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip3 install --upgrade pip
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p inputVideos outputResults temp logs

# Set Python path
ENV PYTHONPATH=/app:/app/openpose/build/python

# Expose API port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command (can be overridden)
CMD ["python3", "-m", "uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
