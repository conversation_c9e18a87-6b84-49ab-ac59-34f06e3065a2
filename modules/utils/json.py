import json
import io
import numpy as np
from pathlib import Path
from typing import Dict, Any


def _sanitize(obj):
    """Recursively sanitize data structure for JSON serialization."""
    if isinstance(obj, dict):
        return {k: _sanitize(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_sanitize(item) for item in obj]
    elif isinstance(obj, (int, str, bool)) or obj is None:
        return obj
    elif isinstance(obj, float):
        if np.isnan(obj) or np.isinf(obj):
            return None
        return obj
    elif isinstance(obj, np.ndarray):
        return _sanitize(obj.tolist())
    elif hasattr(obj, '__dict__'):
        return _sanitize(obj.__dict__)
    else:
        # For other types, try to convert to basic types
        try:
            if hasattr(obj, 'tolist'):
                return _sanitize(obj.tolist())
            elif hasattr(obj, '__iter__') and not isinstance(obj, str):
                return [_sanitize(item) for item in obj]
            else:
                return str(obj)
        except:
            return str(obj)

def dump(results: Dict[str, Any], out_p: Path) -> None:
    """Save analysis results to JSON file."""
    try:
        with out_p.open('w', encoding='utf-8') as f:
            # NOTE: sanitize by default
            json.dump(_sanitize(results), f, indent=2, default=str)
    except Exception as e:
        print(f"Error saving results to {out_p}: {e}")