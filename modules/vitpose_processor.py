"""
ViTPose processor module for pose estimation.
"""
import cv2
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class ViTPoseProcessor:
    """
    ViTPose processor for extracting pose keypoints using ViTPose models.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize ViTPose processor with configuration.

        Args:
            config: Configuration dictionary containing ViTPose settings
        """
        self.config = config
        self.vitpose_config = config.get("vitpose", {})
        self.video_config = config.get("video", {})
        self.gait_config = config.get("gait", {})

        self.pose_model = None
        self.det_model = None
        self._initialize_vitpose()

    def _initialize_vitpose(self) -> None:
        """Initialize ViTPose models."""
        try:
            # Set up ViTPose environment
            from config import setup_vitpose_environment
            setup_vitpose_environment()

            # Import ViTPose dependencies
            from mmpose.apis import init_pose_model, inference_top_down_pose_model
            from mmdet.apis import init_detector, inference_detector
            import mmcv

            # Initialize detection model (for person detection)
            det_config = 'demo/mmdetection_cfg/faster_rcnn_r50_fpn_coco.py'
            det_checkpoint = 'https://download.openmmlab.com/mmdetection/v2.0/faster_rcnn/faster_rcnn_r50_fpn_1x_coco/faster_rcnn_r50_fpn_1x_coco_20200130-047c8118.pth'
            
            self.det_model = init_detector(
                det_config, 
                det_checkpoint, 
                device=self.vitpose_config.get("device", "cuda:0")
            )

            # Initialize pose model
            config_file = self.vitpose_config.get("config_file")
            checkpoint_file = self.vitpose_config.get("checkpoint_file")
            
            if not Path(config_file).exists():
                raise FileNotFoundError(f"ViTPose config file not found: {config_file}")
            if not Path(checkpoint_file).exists():
                raise FileNotFoundError(f"ViTPose checkpoint file not found: {checkpoint_file}")

            self.pose_model = init_pose_model(
                config_file,
                checkpoint_file,
                device=self.vitpose_config.get("device", "cuda:0")
            )

            # Store inference function
            self.inference_pose = inference_top_down_pose_model

            logger.info("ViTPose initialized successfully")

        except ImportError as e:
            logger.error(f"Failed to import ViTPose dependencies: {e}")
            raise RuntimeError("ViTPose dependencies not available. Please check installation.")
        except Exception as e:
            logger.error(f"Failed to initialize ViTPose: {e}")
            raise

    def _process_frame(self, frame: np.ndarray, frame_idx: int, fps: float, visualize: bool = False) -> Tuple[Dict[str, Any], Optional[np.ndarray]]:
        """
        Process single frame with ViTPose.

        Args:
            frame: Input frame
            frame_idx: Frame index
            fps: Video FPS
            visualize: Whether to return visualization frame

        Returns:
            Tuple of (frame data with keypoints, visualization frame if requested)
        """
        try:
            # Detect persons in the frame
            det_results = self.inference_detector(self.det_model, frame)
            
            # Filter person detections
            person_results = []
            if isinstance(det_results, tuple):
                det_results = det_results[0]
            
            if len(det_results) > 0:
                # Get person detections (class 0 in COCO)
                person_dets = det_results[0]  # Class 0 is person
                bbox_thr = self.vitpose_config.get("bbox_thr", 0.3)
                
                for det in person_dets:
                    if len(det) >= 5 and det[4] > bbox_thr:
                        person_results.append({
                            'bbox': det[:4].tolist(),
                            'score': float(det[4])
                        })

            # Perform pose estimation
            pose_results = []
            if person_results:
                pose_results, _ = self.inference_pose(
                    self.pose_model,
                    frame,
                    person_results,
                    bbox_thr=self.vitpose_config.get("bbox_thr", 0.3),
                    format='xyxy'
                )

            # Convert to OpenPose-compatible format
            frame_data = self._convert_to_openpose_format(pose_results, frame_idx, fps)

            # Generate visualization if requested
            vis_frame = None
            if visualize and pose_results:
                vis_frame = self._visualize_pose(frame.copy(), pose_results)

            return frame_data, vis_frame

        except Exception as e:
            logger.error(f"Error processing frame {frame_idx}: {e}")
            # Return empty frame data on error
            return {
                "frame_idx": frame_idx,
                "timestamp": frame_idx / fps,
                "people": []
            }, None

    def _convert_to_openpose_format(self, pose_results: List[Dict], frame_idx: int, fps: float) -> Dict[str, Any]:
        """
        Convert ViTPose results to OpenPose-compatible format.

        Args:
            pose_results: ViTPose pose estimation results
            frame_idx: Frame index
            fps: Video FPS

        Returns:
            Frame data in OpenPose format
        """
        people = []
        
        for pose_result in pose_results:
            keypoints = pose_result.get('keypoints', [])
            if len(keypoints) == 0:
                continue
                
            # ViTPose uses COCO format (17 keypoints), need to convert to BODY_25 format
            # This is a simplified conversion - you may need to adjust based on your specific needs
            body25_keypoints = self._convert_coco_to_body25(keypoints[0])
            
            person_data = {
                "person_id": 0,  # ViTPose doesn't provide person tracking
                "pose_keypoints_2d": body25_keypoints.flatten().tolist(),
                "pose_score": float(pose_result.get('bbox_score', 0.0)),
                "face_keypoints_2d": [],  # ViTPose doesn't provide face keypoints by default
                "hand_left_keypoints_2d": [],  # ViTPose doesn't provide hand keypoints by default
                "hand_right_keypoints_2d": []
            }
            people.append(person_data)

        return {
            "frame_idx": frame_idx,
            "timestamp": frame_idx / fps,
            "people": people
        }

    def _convert_coco_to_body25(self, coco_keypoints: np.ndarray) -> np.ndarray:
        """
        Convert COCO 17-keypoint format to OpenPose BODY_25 format.
        
        Args:
            coco_keypoints: COCO keypoints array (17, 3)
            
        Returns:
            BODY_25 keypoints array (25, 3)
        """
        # Initialize BODY_25 keypoints array
        body25_keypoints = np.zeros((25, 3))
        
        # COCO to BODY_25 mapping (simplified)
        # This mapping may need adjustment based on your specific requirements
        coco_to_body25_map = {
            0: 0,   # Nose -> Nose
            1: 15,  # Left Eye -> Left Eye
            2: 14,  # Right Eye -> Right Eye
            3: 17,  # Left Ear -> Left Ear
            4: 16,  # Right Ear -> Right Ear
            5: 5,   # Left Shoulder -> Left Shoulder
            6: 2,   # Right Shoulder -> Right Shoulder
            7: 6,   # Left Elbow -> Left Elbow
            8: 3,   # Right Elbow -> Right Elbow
            9: 7,   # Left Wrist -> Left Wrist
            10: 4,  # Right Wrist -> Right Wrist
            11: 12, # Left Hip -> Left Hip
            12: 9,  # Right Hip -> Right Hip
            13: 13, # Left Knee -> Left Knee
            14: 10, # Right Knee -> Right Knee
            15: 14, # Left Ankle -> Left Ankle
            16: 11, # Right Ankle -> Right Ankle
        }
        
        # Map COCO keypoints to BODY_25 format
        for coco_idx, body25_idx in coco_to_body25_map.items():
            if coco_idx < len(coco_keypoints):
                body25_keypoints[body25_idx] = coco_keypoints[coco_idx]
        
        return body25_keypoints

    def _visualize_pose(self, frame: np.ndarray, pose_results: List[Dict]) -> np.ndarray:
        """
        Visualize pose estimation results on frame.

        Args:
            frame: Input frame
            pose_results: Pose estimation results

        Returns:
            Frame with pose visualization
        """
        try:
            from mmpose.apis import vis_pose_result
            
            vis_frame = vis_pose_result(
                self.pose_model,
                frame,
                pose_results,
                kpt_score_thr=self.vitpose_config.get("kpt_thr", 0.3),
                show=False
            )
            return vis_frame
        except Exception as e:
            logger.error(f"Error visualizing pose: {e}")
            return frame

    def inference_detector(self, model, frame):
        """Wrapper for mmdet inference_detector to handle imports."""
        try:
            from mmdet.apis import inference_detector
            return inference_detector(model, frame)
        except ImportError:
            logger.error("MMDetection not available for person detection")
            return []
