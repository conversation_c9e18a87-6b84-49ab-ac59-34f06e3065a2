"""
Biomechanical analyzer for calculating 14 specific gait measurements.
"""
import numpy as np
from typing import List, Dict, Any
import logging
from scipy.stats import linregress

from .utils.general import (
    euclidean_distance,
    calculate_angle,
    estimate_body_height,
    normalize_by_body_height,
    apply_butterworth_filter
)

logger = logging.getLogger(__name__)

class BiomechanicalAnalyzer:
    """
    Analyzer for calculating biomechanical measurements from gait data.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize biomechanical analyzer.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.keypoint_indices = config.get("keypoints", {})
        self.measurement_config = config.get("measurement", {})

    def analyze_gait_measurements(self, keypoints_sequence: List[np.ndarray],
                                gait_data: Dict[str, Any], fps: float) -> Dict[str, Any]:
        """
        Calculate all 14 biomechanical measurements.

        Args:
            keypoints_sequence: List of keypoint arrays for each frame
            gait_data: Gait cycle detection results
            fps: Video frame rate

        Returns:
            Dictionary containing all measurements
        """
        logger.info("Calculating biomechanical measurements...")

        if not keypoints_sequence:
            logger.warning("No keypoints data provided")
            return {}

        # Estimate body height for normalization
        body_height = self._estimate_average_body_height(keypoints_sequence)

        # Calculate each measurement
        measurements = {}

        try:
            # 1. Pelvic Drop
            measurements["pelvicDrop"] = self.calculate_pelvic_drop(keypoints_sequence, gait_data)

            # 2. Pronation
            measurements["Pronation"] = self.calculate_pronation(keypoints_sequence, gait_data)

            # 3. Knee Drift
            measurements["kneeDrift"] = self.calculate_knee_drift(keypoints_sequence, gait_data)

            # 4. Foot Drift
            measurements["footDrift"] = self.calculate_foot_drift(keypoints_sequence, gait_data)

            # 5. Heel Whip
            measurements["heelWhip"] = self.calculate_heel_whip(keypoints_sequence, gait_data)

            # 6. Posterior Stability Score (composite)
            measurements["posteriorStabilityScore"] = self.calculate_posterior_stability_score(measurements)

            # 7. Overstride
            measurements["overstride"] = self.calculate_overstride(keypoints_sequence, gait_data, body_height)

            # 8. Ankle Dorsiflexion
            measurements["ankleDorsiflexion"] = self.calculate_ankle_dorsiflexion(keypoints_sequence, gait_data)

            # 9. Ankle Plantarflexion
            measurements["anklePlantarflexion"] = self.calculate_ankle_plantarflexion(keypoints_sequence, gait_data)

            # 10. Vertical Oscillation
            measurements["verticalOscillationVideo"] = self.calculate_vertical_oscillation(keypoints_sequence, body_height)

            # 11. Trunk Lean
            measurements["trunkLean"] = self.calculate_trunk_lean(keypoints_sequence, gait_data)

            # 12. Ground Contact Time
            measurements["Ground_contact_Time"] = self.calculate_ground_contact_time(gait_data, fps)

            # 13. Cadence
            measurements["Cadence"] = self.calculate_cadence(gait_data, fps)

            # 14. Knee Flexion Loading
            measurements["kneeFlexionLoading"] = self.calculate_knee_flexion_loading(keypoints_sequence, gait_data)

            # Add metadata
            measurements["body_height_pixels"] = body_height
            measurements["total_frames"] = len(keypoints_sequence)
            measurements["fps"] = fps
            measurements["analysis_method"] = "openpose_body25"

        except Exception as e:
            logger.error(f"Error calculating measurements: {e}")
            # Return partial results with error information
            measurements["error"] = str(e)

        logger.info("Biomechanical analysis completed")
        return measurements

    def calculate_pelvic_drop(self, keypoints_sequence: List[np.ndarray],
                            gait_data: Dict[str, Any]) -> float:
        """
        Calculate pelvic drop direction analysis.
        Measures the lateral tilt of the pelvis during stance phase.
        """
        try:
            pelvic_drops = []

            for cycle in gait_data.get("cycles", []):
                start_frame = cycle["start_frame"]
                end_frame = cycle["end_frame"]

                # Extract hip positions during this cycle
                left_hip_positions = []
                right_hip_positions = []

                for frame_idx in range(start_frame, min(end_frame, len(keypoints_sequence))):
                    keypoints = keypoints_sequence[frame_idx]

                    left_hip = keypoints[self.keypoint_indices["LHip"], :2]
                    right_hip = keypoints[self.keypoint_indices["RHip"], :2]

                    if not (np.isnan(left_hip).any() or np.isnan(right_hip).any()):
                        left_hip_positions.append(left_hip)
                        right_hip_positions.append(right_hip)

                if len(left_hip_positions) > 5:
                    # Calculate pelvic tilt angles
                    tilt_angles = []
                    for left_hip, right_hip in zip(left_hip_positions, right_hip_positions):
                        # Calculate angle of line connecting hips relative to horizontal
                        dx = right_hip[0] - left_hip[0]
                        dy = right_hip[1] - left_hip[1]
                        angle = np.degrees(np.arctan2(dy, dx))
                        tilt_angles.append(angle)

                    # Calculate range of pelvic tilt
                    if tilt_angles:
                        pelvic_drop = np.max(tilt_angles) - np.min(tilt_angles)
                        pelvic_drops.append(pelvic_drop)

            return float(np.mean(pelvic_drops)) if pelvic_drops else 0.0

        except Exception as e:
            logger.error(f"Error calculating pelvic drop: {e}")
            return 0.0

    def calculate_pronation(self, keypoints_sequence: List[np.ndarray],
                          gait_data: Dict[str, Any]) -> float:
        """
        Calculate ankle pronation/supination angle between knee and heel.
        """
        try:
            pronation_angles = []

            for cycle in gait_data.get("cycles", []):
                start_frame = cycle["start_frame"]
                end_frame = cycle["end_frame"]
                foot = cycle["foot"]

                # Select appropriate keypoints based on foot
                if foot == "left":
                    knee_idx = self.keypoint_indices["LKnee"]
                    ankle_idx = self.keypoint_indices["LAnkle"]
                    heel_idx = self.keypoint_indices["LHeel"]
                else:
                    knee_idx = self.keypoint_indices["RKnee"]
                    ankle_idx = self.keypoint_indices["RAnkle"]
                    heel_idx = self.keypoint_indices["RHeel"]

                cycle_angles = []
                for frame_idx in range(start_frame, min(end_frame, len(keypoints_sequence))):
                    keypoints = keypoints_sequence[frame_idx]

                    knee = keypoints[knee_idx, :2]
                    ankle = keypoints[ankle_idx, :2]
                    heel = keypoints[heel_idx, :2]

                    if not (np.isnan(knee).any() or np.isnan(ankle).any() or np.isnan(heel).any()):
                        # Calculate angle at ankle
                        angle = calculate_angle(knee, ankle, heel)
                        cycle_angles.append(angle)

                if cycle_angles:
                    # Use the minimum angle during stance phase as pronation measure
                    pronation_angles.append(np.min(cycle_angles))

            return float(np.mean(pronation_angles)) if pronation_angles else 0.0

        except Exception as e:
            logger.error(f"Error calculating pronation: {e}")
            return 0.0

    def calculate_knee_drift(self, keypoints_sequence: List[np.ndarray],
                           gait_data: Dict[str, Any]) -> float:
        """
        Calculate knee valgus/varus at midstance - ratio of inter-knee to inter-hip distances.
        """
        try:
            knee_drift_ratios = []

            for cycle in gait_data.get("cycles", []):
                start_frame = cycle["start_frame"]
                end_frame = cycle["end_frame"]

                # Find midstance frame (approximately middle of stance phase)
                midstance_frame = start_frame + (end_frame - start_frame) // 3

                if midstance_frame < len(keypoints_sequence):
                    keypoints = keypoints_sequence[midstance_frame]

                    left_hip = keypoints[self.keypoint_indices["LHip"], :2]
                    right_hip = keypoints[self.keypoint_indices["RHip"], :2]
                    left_knee = keypoints[self.keypoint_indices["LKnee"], :2]
                    right_knee = keypoints[self.keypoint_indices["RKnee"], :2]

                    if not any(np.isnan(pos).any() for pos in [left_hip, right_hip, left_knee, right_knee]):
                        # Calculate distances
                        inter_hip_distance = euclidean_distance(left_hip, right_hip)
                        inter_knee_distance = euclidean_distance(left_knee, right_knee)

                        if inter_hip_distance > 0:
                            knee_drift_ratio = inter_knee_distance / inter_hip_distance
                            knee_drift_ratios.append(knee_drift_ratio)

            return float(np.mean(knee_drift_ratios)) if knee_drift_ratios else 1.0

        except Exception as e:
            logger.error(f"Error calculating knee drift: {e}")
            return 1.0

    def calculate_foot_drift(self, keypoints_sequence: List[np.ndarray],
                           gait_data: Dict[str, Any]) -> float:
        """
        Calculate foot crossover gait detection.
        Measures lateral deviation of foot placement.
        """
        try:
            foot_drifts = []

            # Calculate center line of movement
            all_hip_centers = []
            for keypoints in keypoints_sequence:
                left_hip = keypoints[self.keypoint_indices["LHip"], :2]
                right_hip = keypoints[self.keypoint_indices["RHip"], :2]

                if not (np.isnan(left_hip).any() or np.isnan(right_hip).any()):
                    hip_center = ((left_hip[0] + right_hip[0]) / 2, (left_hip[1] + right_hip[1]) / 2)
                    all_hip_centers.append(hip_center)

            if len(all_hip_centers) < 10:
                return 0.0

            # Calculate average center line x-coordinate
            center_line_x = np.mean([center[0] for center in all_hip_centers])

            # Measure foot placement relative to center line
            for cycle in gait_data.get("cycles", []):
                start_frame = cycle["start_frame"]
                foot = cycle["foot"]

                if start_frame < len(keypoints_sequence):
                    keypoints = keypoints_sequence[start_frame]

                    if foot == "left":
                        foot_pos = keypoints[self.keypoint_indices["LAnkle"], :2]
                    else:
                        foot_pos = keypoints[self.keypoint_indices["RAnkle"], :2]

                    if not np.isnan(foot_pos).any():
                        # Calculate lateral deviation from center line
                        lateral_deviation = abs(foot_pos[0] - center_line_x)
                        foot_drifts.append(lateral_deviation)

            return float(np.mean(foot_drifts)) if foot_drifts else 0.0

        except Exception as e:
            logger.error(f"Error calculating foot drift: {e}")
            return 0.0

    def calculate_heel_whip(self, keypoints_sequence: List[np.ndarray],
                          gait_data: Dict[str, Any]) -> float:
        """
        Calculate medial/lateral heel movement relative to midline.
        """
        try:
            heel_whips = []

            for cycle in gait_data.get("cycles", []):
                start_frame = cycle["start_frame"]
                end_frame = cycle["end_frame"]
                foot = cycle["foot"]

                # Select heel keypoint
                if foot == "left":
                    heel_idx = self.keypoint_indices["LHeel"]
                else:
                    heel_idx = self.keypoint_indices["RHeel"]

                # Extract heel positions during swing phase
                heel_positions = []
                for frame_idx in range(start_frame, min(end_frame, len(keypoints_sequence))):
                    keypoints = keypoints_sequence[frame_idx]
                    heel = keypoints[heel_idx, :2]

                    if not np.isnan(heel).any():
                        heel_positions.append(heel)

                if len(heel_positions) > 5:
                    # Calculate lateral movement range
                    x_positions = [pos[0] for pos in heel_positions]
                    heel_whip = np.max(x_positions) - np.min(x_positions)
                    heel_whips.append(heel_whip)

            return float(np.mean(heel_whips)) if heel_whips else 0.0

        except Exception as e:
            logger.error(f"Error calculating heel whip: {e}")
            return 0.0

    def calculate_posterior_stability_score(self, measurements: Dict[str, float]) -> float:
        """
        Calculate composite posterior stability score.
        Formula: 0.4×footDrift + 0.3×kneeDrift + 0.2×heelWhip + 0.1×pelvicDrop
        """
        try:
            foot_drift = measurements.get("footDrift", 0.0)
            knee_drift = measurements.get("kneeDrift", 1.0)
            heel_whip = measurements.get("heelWhip", 0.0)
            pelvic_drop = measurements.get("pelvicDrop", 0.0)

            # Normalize knee_drift (subtract 1.0 since 1.0 is ideal)
            knee_drift_normalized = abs(knee_drift - 1.0)

            score = (0.4 * foot_drift +
                    0.3 * knee_drift_normalized +
                    0.2 * heel_whip +
                    0.1 * pelvic_drop)

            return float(score)

        except Exception as e:
            logger.error(f"Error calculating posterior stability score: {e}")
            return 0.0

    def calculate_overstride(self, keypoints_sequence: List[np.ndarray],
                           gait_data: Dict[str, Any], body_height: float) -> float:
        """
        Calculate overstride at initial contact.
        Formula: (LHeel_x - LHip_x) / euclidean_distance(LHip, LKnee)
        """
        try:
            overstride_values = []

            for event in gait_data.get("ic_events", []):
                frame_idx = event["frame"]
                foot = event["foot"]

                if frame_idx < len(keypoints_sequence):
                    keypoints = keypoints_sequence[frame_idx]

                    if foot == "left":
                        heel = keypoints[self.keypoint_indices["LHeel"], :2]
                        hip = keypoints[self.keypoint_indices["LHip"], :2]
                        knee = keypoints[self.keypoint_indices["LKnee"], :2]
                    else:
                        heel = keypoints[self.keypoint_indices["RHeel"], :2]
                        hip = keypoints[self.keypoint_indices["RHip"], :2]
                        knee = keypoints[self.keypoint_indices["RKnee"], :2]

                    if not any(np.isnan(pos).any() for pos in [heel, hip, knee]):
                        # Calculate horizontal distance from heel to hip
                        heel_hip_distance = heel[0] - hip[0]

                        # Calculate hip-knee distance for normalization
                        hip_knee_distance = euclidean_distance(hip, knee)

                        if hip_knee_distance > 0:
                            overstride = heel_hip_distance / hip_knee_distance
                            overstride_values.append(overstride)

            return float(np.mean(overstride_values)) if overstride_values else 0.0

        except Exception as e:
            logger.error(f"Error calculating overstride: {e}")
            return 0.0

    def calculate_ankle_dorsiflexion(self, keypoints_sequence: List[np.ndarray],
                                   gait_data: Dict[str, Any]) -> float:
        """
        Calculate ankle dorsiflexion angle for foot strike type classification.
        """
        try:
            dorsiflexion_angles = []

            for event in gait_data.get("ic_events", []):
                frame_idx = event["frame"]
                foot = event["foot"]

                if frame_idx < len(keypoints_sequence):
                    keypoints = keypoints_sequence[frame_idx]

                    if foot == "left":
                        knee = keypoints[self.keypoint_indices["LKnee"], :2]
                        ankle = keypoints[self.keypoint_indices["LAnkle"], :2]
                        toe = keypoints[self.keypoint_indices["LBigToe"], :2]
                    else:
                        knee = keypoints[self.keypoint_indices["RKnee"], :2]
                        ankle = keypoints[self.keypoint_indices["RAnkle"], :2]
                        toe = keypoints[self.keypoint_indices["RBigToe"], :2]

                    if not any(np.isnan(pos).any() for pos in [knee, ankle, toe]):
                        # Calculate ankle angle (dorsiflexion)
                        angle = calculate_angle(knee, ankle, toe)
                        dorsiflexion_angles.append(angle)

            return float(np.mean(dorsiflexion_angles)) if dorsiflexion_angles else 90.0

        except Exception as e:
            logger.error(f"Error calculating ankle dorsiflexion: {e}")
            return 90.0

    def calculate_ankle_plantarflexion(self, keypoints_sequence: List[np.ndarray],
                                     gait_data: Dict[str, Any]) -> float:
        """
        Calculate ankle plantarflexion for propulsive power and push-off mechanics.
        """
        try:
            plantarflexion_angles = []

            for event in gait_data.get("to_events", []):
                frame_idx = event["frame"]
                foot = event["foot"]

                if frame_idx < len(keypoints_sequence):
                    keypoints = keypoints_sequence[frame_idx]

                    if foot == "left":
                        knee = keypoints[self.keypoint_indices["LKnee"], :2]
                        ankle = keypoints[self.keypoint_indices["LAnkle"], :2]
                        toe = keypoints[self.keypoint_indices["LBigToe"], :2]
                    else:
                        knee = keypoints[self.keypoint_indices["RKnee"], :2]
                        ankle = keypoints[self.keypoint_indices["RAnkle"], :2]
                        toe = keypoints[self.keypoint_indices["RBigToe"], :2]

                    if not any(np.isnan(pos).any() for pos in [knee, ankle, toe]):
                        # Calculate ankle angle (plantarflexion)
                        angle = calculate_angle(knee, ankle, toe)
                        plantarflexion_angles.append(angle)

            return float(np.mean(plantarflexion_angles)) if plantarflexion_angles else 90.0

        except Exception as e:
            logger.error(f"Error calculating ankle plantarflexion: {e}")
            return 90.0

    def calculate_vertical_oscillation(self, keypoints_sequence: List[np.ndarray],
                                     body_height: float) -> float:
        """
        Calculate vertical oscillation in centimeters.
        """
        try:
            # Extract hip center positions
            hip_center_y = []

            for keypoints in keypoints_sequence:
                left_hip = keypoints[self.keypoint_indices["LHip"], :2]
                right_hip = keypoints[self.keypoint_indices["RHip"], :2]

                if not (np.isnan(left_hip).any() or np.isnan(right_hip).any()):
                    center_y = (left_hip[1] + right_hip[1]) / 2
                    hip_center_y.append(center_y)

            if len(hip_center_y) < 10:
                return 0.0

            # Apply smoothing
            hip_center_y_smooth = apply_butterworth_filter(np.array(hip_center_y))

            # Calculate vertical oscillation range
            vertical_range_pixels = np.max(hip_center_y_smooth) - np.min(hip_center_y_smooth)

            # Convert to centimeters (assuming average body height of 170cm)
            # This is a rough conversion - would need calibration for accurate results
            vertical_oscillation_cm = (vertical_range_pixels / body_height) * 170.0

            return float(vertical_oscillation_cm)

        except Exception as e:
            logger.error(f"Error calculating vertical oscillation: {e}")
            return 0.0

    def calculate_trunk_lean(self, keypoints_sequence: List[np.ndarray],
                           gait_data: Dict[str, Any]) -> float:
        """
        Calculate trunk lean angle in degrees.
        """
        try:
            trunk_lean_angles = []

            for keypoints in keypoints_sequence:
                neck = keypoints[self.keypoint_indices["Neck"], :2]
                mid_hip = keypoints[self.keypoint_indices["MidHip"], :2]

                if not (np.isnan(neck).any() or np.isnan(mid_hip).any()):
                    # Calculate angle of trunk relative to vertical
                    dx = neck[0] - mid_hip[0]
                    dy = neck[1] - mid_hip[1]

                    # Calculate angle from vertical (90 degrees is perfectly upright)
                    trunk_angle = np.degrees(np.arctan2(abs(dx), abs(dy)))
                    trunk_lean_angles.append(trunk_angle)

            return float(np.mean(trunk_lean_angles)) if trunk_lean_angles else 0.0

        except Exception as e:
            logger.error(f"Error calculating trunk lean: {e}")
            return 0.0

    def calculate_ground_contact_time(self, gait_data: Dict[str, Any], fps: float) -> float:
        """
        Calculate ground contact time in milliseconds.
        IC to TO duration = (frame_count / fps) × 1000
        """
        try:
            contact_times = []

            ic_events = gait_data.get("ic_events", [])
            to_events = gait_data.get("to_events", [])

            for ic_event in ic_events:
                foot = ic_event["foot"]
                ic_frame = ic_event["frame"]

                # Find corresponding TO event for the same foot
                matching_to_events = [
                    to for to in to_events
                    if to["foot"] == foot and to["frame"] > ic_frame
                ]

                if matching_to_events:
                    # Take the first TO event after this IC
                    to_event = min(matching_to_events, key=lambda x: x["frame"])
                    to_frame = to_event["frame"]

                    # Calculate contact time
                    frame_count = to_frame - ic_frame
                    contact_time_ms = (frame_count / fps) * 1000
                    contact_times.append(contact_time_ms)

            return float(np.mean(contact_times)) if contact_times else 0.0

        except Exception as e:
            logger.error(f"Error calculating ground contact time: {e}")
            return 0.0

    def calculate_cadence(self, gait_data: Dict[str, Any], fps: float) -> float:
        """
        Calculate cadence in steps per second.
        Formula: total_time / (IC_frames - 1)
        """
        try:
            ic_events = gait_data.get("ic_events", [])

            if len(ic_events) < 2:
                return 0.0

            # Calculate time span
            first_ic = ic_events[0]["frame"]
            last_ic = ic_events[-1]["frame"]

            total_time_seconds = (last_ic - first_ic) / fps
            total_steps = len(ic_events) - 1

            if total_time_seconds > 0:
                cadence = total_steps / total_time_seconds
                return float(cadence)

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating cadence: {e}")
            return 0.0

    def calculate_knee_flexion_loading(self, keypoints_sequence: List[np.ndarray],
                                     gait_data: Dict[str, Any]) -> float:
        """
        Calculate knee flexion angle during loading phase.
        """
        try:
            knee_flexion_angles = []

            for cycle in gait_data.get("cycles", []):
                start_frame = cycle["start_frame"]
                end_frame = cycle["end_frame"]
                foot = cycle["foot"]

                # Loading phase is approximately first 25% of stance phase
                loading_end_frame = start_frame + (end_frame - start_frame) // 4

                if foot == "left":
                    hip_idx = self.keypoint_indices["LHip"]
                    knee_idx = self.keypoint_indices["LKnee"]
                    ankle_idx = self.keypoint_indices["LAnkle"]
                else:
                    hip_idx = self.keypoint_indices["RHip"]
                    knee_idx = self.keypoint_indices["RKnee"]
                    ankle_idx = self.keypoint_indices["RAnkle"]

                loading_angles = []
                for frame_idx in range(start_frame, min(loading_end_frame, len(keypoints_sequence))):
                    keypoints = keypoints_sequence[frame_idx]

                    hip = keypoints[hip_idx, :2]
                    knee = keypoints[knee_idx, :2]
                    ankle = keypoints[ankle_idx, :2]

                    if not any(np.isnan(pos).any() for pos in [hip, knee, ankle]):
                        # Calculate knee flexion angle
                        angle = calculate_angle(hip, knee, ankle)
                        loading_angles.append(angle)

                if loading_angles:
                    # Use maximum flexion during loading phase
                    max_flexion = np.max(loading_angles)
                    knee_flexion_angles.append(max_flexion)

            return float(np.mean(knee_flexion_angles)) if knee_flexion_angles else 180.0

        except Exception as e:
            logger.error(f"Error calculating knee flexion loading: {e}")
            return 180.0

    def _estimate_average_body_height(self, keypoints_sequence: List[np.ndarray]) -> float:
        """Estimate average body height across all frames."""
        heights = []

        for keypoints in keypoints_sequence:
            height = estimate_body_height(keypoints)
            if height > 0:
                heights.append(height)

        return np.mean(heights) if heights else 100.0
