"""
Gait analysis modules for video processing and biomechanical measurements.
"""

__version__ = "1.0.0"
__author__ = "Gait Analysis Platform"

# Import utils as a package
from . import utils

# Import main module classes
from .video_processor import VideoProcessor
from .gait_cycle_detector import Gait<PERSON>ycleDetector
from .biomechanical_analyzer import BiomechanicalAnalyzer
from .visualizer import GaitVisualizer

__all__ = [
    "VideoProcessor",
    "GaitCycleDetector",
    "BiomechanicalAnalyzer",
    "GaitVisualizer",
]
