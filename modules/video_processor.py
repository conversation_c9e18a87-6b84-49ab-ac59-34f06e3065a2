"""
Video processing module supporting both OpenPose and ViTPose for pose estimation.
"""
import cv2
import logging
import imageio
import numpy as np
from tqdm import tqdm
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from . import utils

logger = logging.getLogger(__name__)

class VideoProcessor:
    """
    Video processor for extracting pose keypoints using OpenPose or ViTPose.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize video processor with configuration.

        Args:
            config: Configuration dictionary containing pose detection and video settings
        """
        self.config = config
        self.pose_engine = config.get("pose_detection_engine", "openpose")
        self.openpose_config = config.get("openpose", {})
        self.vitpose_config = config.get("vitpose", {})
        self.video_config = config.get("video", {})
        self.gait_config = config.get("gait", {})

        self.pose_processor = None
        self._initialize_pose_processor()

    def _initialize_pose_processor(self) -> None:
        """Initialize the selected pose detection processor."""
        if self.pose_engine == "openpose":
            self._initialize_openpose()
        elif self.pose_engine == "vitpose":
            self._initialize_vitpose()
        else:
            raise ValueError(f"Unsupported pose detection engine: {self.pose_engine}")

    def _initialize_openpose(self) -> None:
        """Initialize OpenPose wrapper."""
        try:
            from config import setup_openpose_environment
            setup_openpose_environment()
            from openpose import pyopenpose as op
        except ImportError as e:
            logger.error(f"Failed to import OpenPose: {e}")
            raise RuntimeError("OpenPose not available. Please check installation.")

        try:
            # Configure OpenPose parameters
            params = dict()
            params.update(self.openpose_config)

            # Initialize wrapper
            op_wrapper = op.WrapperPython()
            op_wrapper.configure(params)
            op_wrapper.start()

            # Store the wrapper and processing function
            self.pose_processor = {
                'wrapper': op_wrapper,
                'process_func': self._process_frame_openpose,
                'type': 'openpose'
            }

            logger.info("OpenPose initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize OpenPose: {e}")
            raise

    def _initialize_vitpose(self) -> None:
        """Initialize ViTPose processor."""
        try:
            from .vitpose_processor import ViTPoseProcessor

            vitpose_processor = ViTPoseProcessor(self.config)

            # Store the processor and processing function
            self.pose_processor = {
                'processor': vitpose_processor,
                'process_func': self._process_frame_vitpose,
                'type': 'vitpose'
            }

            logger.info("ViTPose initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize ViTPose: {e}")
            raise

    def process_video(self, video_p: Path, output_dir: Path, visualize: bool = False) -> Dict[str, Any]:
        """
        Process video and extract pose keypoints.

        Args:
            video_path: Path to input video
            output_dir: Directory to save results
            visualize: Whether to save visualization frames and video

        Returns:
            Dictionary containing processing results and metadata
        """
        logger.info(f"Processing video: {video_p}")

        # Validate video file
        if not utils.video.validate(video_p, self.video_config.get("supported_formats", [])):
            raise ValueError(f"Invalid video file: {video_p}")

        # Get video properties
        video_props = utils.video.probe(video_p)
        if not video_props:
            raise ValueError(f"Cannot read video properties: {video_p}")

        logger.info(f"Video properties: {video_props}")

        # Check video constraints
        if video_props["fps"] < self.video_config.get("fps_threshold", 24):
            logger.warning(f"Low FPS detected: {video_props['fps']}")

        if video_props["duration"] < self.video_config.get("min_duration", 2.0):
            raise ValueError(f"Video too short: {video_props['duration']}s (Min. 2 seconds required)")

        if video_props["duration"] > self.video_config.get("max_duration", 300.0):
            raise ValueError(f"Video too long: {video_props['duration']}s (Max. 5 minutes allowed)")

        # Process video frames
        keypoints_data = self._extract_keypoints(video_p, output_dir, visualize)

        # Prepare results
        processing_config = {
            "pose_detection_engine": self.pose_engine,
            "video": self.video_config,
        }
        if self.pose_engine == "openpose":
            processing_config["openpose"] = self.openpose_config
        elif self.pose_engine == "vitpose":
            processing_config["vitpose"] = self.vitpose_config

        results = {
            "video_path": str(video_p),
            "video_properties": video_props,
            "processing_config": processing_config,
            "keypoints_data": keypoints_data,
            "total_frames": len(keypoints_data),
            "frames_with_detection": sum(1 for frame in keypoints_data if frame["people"]),
        }

        # Save results
        results_path = output_dir / "pose_keypoints.json"
        utils.json.dump(results, results_path)

        logger.info(f"Video processing completed. Results saved to {results_path}")
        return results

    def _extract_keypoints(self, video_path: Path, output_dir: Path, visualize: bool = False) -> List[Dict[str, Any]]:
        """
        Extract keypoints from video frames.

        Args:
            video_path: Path to video file
            output_dir: Output directory for frame-by-frame JSON files
            visualize: Whether to save visualization frames and video

        Returns:
            List of keypoint data for each frame
        """
        keypoints_data = []

        # Create subdirectory for frame JSON files
        frames_dir = output_dir / "frames"
        frames_dir.mkdir(exist_ok=True)

        # Open video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        video_writer = None
        if visualize:
            output_video_path = output_dir / "pose_visualization.mp4"
            video_writer = imageio.get_writer(output_video_path, **utils.video.get_writer_cfg(fps=15))
            logger.info(f"Initialized video writer for visualization: {output_video_path}")

        frame_idx = 0
        with tqdm(total=total_frames, desc="Processing frames") as pbar:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # Resize frame if needed
                if self.video_config.get("resize_height"):
                    height = self.video_config["resize_height"]
                    aspect_ratio = frame.shape[1] / frame.shape[0]
                    width = int(height * aspect_ratio)
                    frame = cv2.resize(frame, (width, height))

                # Process frame using selected pose detection engine
                process_func = self.pose_processor['process_func']
                frame_data, viz_frame = process_func(frame, frame_idx, fps, visualize)
                keypoints_data.append(frame_data)

                # Save frame data to individual JSON file
                frame_json_path = frames_dir / f"frame_{frame_idx:06d}.json"
                utils.json.dump(frame_data, frame_json_path)

                # Save visualization frame if requested
                if visualize and viz_frame is not None:
                    # Convert BGR to RGB for imageio video writer
                    viz_frame_rgb = cv2.cvtColor(viz_frame, cv2.COLOR_BGR2RGB)

                    # Write RGB frames to video
                    if video_writer is not None:
                        video_writer.append_data(viz_frame_rgb)

                frame_idx += 1
                pbar.update(1)

        if video_writer is not None:
            video_writer.close()
            logger.info(f"Visualization video saved to: {output_video_path}")

        cap.release()

        if video_writer is not None:
            video_writer.close()
            logger.info(f"Visualization video saved to: {output_video_path}")

        logger.info(f"Processed {frame_idx} frames")

        return keypoints_data

    def _process_frame_openpose(self, frame: np.ndarray, frame_idx: int, fps: float, visualize: bool = False) -> Tuple[Dict[str, Any], Optional[np.ndarray]]:
        """
        Process single frame with OpenPose.

        Args:
            frame: Input frame
            frame_idx: Frame index
            fps: Video FPS
            visualize: Whether to return visualization frame

        Returns:
            Tuple of (frame data with keypoints, visualization frame if requested)
        """
        try:
            from openpose import pyopenpose as op

            # Create OpenPose datum
            datum = op.Datum()
            datum.cvInputData = frame

            # Process frame
            op_wrapper = self.pose_processor['wrapper']
            op_wrapper.emplaceAndPop(op.VectorDatum([datum]))

            # Extract keypoints
            people_data = []
            if datum.poseKeypoints is not None and len(datum.poseKeypoints.shape) >= 2:
                for person_idx in range(datum.poseKeypoints.shape[0]):
                    person_keypoints = datum.poseKeypoints[person_idx]

                    # Convert to list format for JSON serialization
                    keypoints_list = []
                    for kp_idx in range(person_keypoints.shape[0]):
                        x, y, confidence = person_keypoints[kp_idx]
                        keypoints_list.extend([float(x), float(y), float(confidence)])

                    people_data.append({
                        "person_id": person_idx,
                        "pose_keypoints_2d": keypoints_list,
                        "face_keypoints_2d": [],  # Not used for gait analysis
                        "hand_left_keypoints_2d": [],  # Not used for gait analysis
                        "hand_right_keypoints_2d": [],  # Not used for gait analysis
                    })

            frame_data = {
                "version": 1.3,
                "frame_idx": frame_idx,
                "timestamp": frame_idx / fps if fps > 0 else 0,
                "people": people_data,
            }

            # Get visualization frame if requested
            viz_frame = None
            if visualize and datum.cvOutputData is not None:
                viz_frame = datum.cvOutputData.copy()

            return frame_data, viz_frame

        except Exception as e:
            logger.error(f"Error processing frame {frame_idx}: {e}")
            frame_data = {
                "version": 1.3,
                "frame_idx": frame_idx,
                "timestamp": frame_idx / fps if fps > 0 else 0,
                "people": [],
            }
            return frame_data, None

    def _process_frame_vitpose(self, frame: np.ndarray, frame_idx: int, fps: float, visualize: bool = False) -> Tuple[Dict[str, Any], Optional[np.ndarray]]:
        """
        Process single frame with ViTPose.

        Args:
            frame: Input frame
            frame_idx: Frame index
            fps: Video FPS
            visualize: Whether to return visualization frame

        Returns:
            Tuple of (frame data with keypoints, visualization frame if requested)
        """
        vitpose_processor = self.pose_processor['processor']
        return vitpose_processor._process_frame(frame, frame_idx, fps, visualize)

    def get_primary_person_keypoints(self, keypoints_data: List[Dict[str, Any]]) -> List[np.ndarray]:
        """
        Extract keypoints for the primary person (most consistently detected) across frames.

        Args:
            keypoints_data: List of frame data with keypoints

        Returns:
            List of keypoint arrays for primary person
        """
        primary_keypoints = []

        for frame_data in keypoints_data:
            people = frame_data.get("people", [])

            if not people:
                # No detection in this frame
                primary_keypoints.append(np.full((25, 3), np.nan))
                continue

            # For now, take the first person (could be improved with tracking)
            person_data = people[0]
            keypoints_flat = person_data.get("pose_keypoints_2d", [])

            if len(keypoints_flat) >= 75:  # 25 keypoints * 3 values each
                # Reshape to (25, 3) format
                keypoints = np.array(keypoints_flat).reshape(25, 3)
                primary_keypoints.append(keypoints)
            else:
                # Invalid keypoints
                primary_keypoints.append(np.full((25, 3), np.nan))

        return primary_keypoints

    def cleanup(self) -> None:
        """Cleanup pose detection resources."""
        if self.pose_processor:
            try:
                if self.pose_processor['type'] == 'openpose':
                    op_wrapper = self.pose_processor.get('wrapper')
                    if op_wrapper:
                        op_wrapper.stop()
                        logger.info("OpenPose wrapper stopped")
                elif self.pose_processor['type'] == 'vitpose':
                    # ViTPose doesn't require explicit cleanup
                    logger.info("ViTPose processor cleaned up")
            except Exception as e:
                logger.error(f"Error cleaning up pose processor: {e}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        self.cleanup()
